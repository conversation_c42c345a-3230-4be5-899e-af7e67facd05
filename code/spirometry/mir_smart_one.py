import asyncio
from bleak import BleakClient, BleakScanner
from mir_ble.decoder import MirSpiroDecoder

# Replace with your MIR Smart One MAC address
DEVICE_ADDRESS = "CF:C3:A0:A2:BE:15"

UUIDS = {
    "COMMAND": "92b403f0-b665-11e3-a5e2-0800200c9a66",
    "FEV1": "f9f84150-b667-11e3-a5e2-0800200c9a66",
    "FVC": "f9f84151-b667-11e3-a5e2-0800200c9a66",
    "FEV1_FVC": "f9f84152-b667-11e3-a5e2-0800200c9a66",
    "PEF": "f9f84153-b667-11e3-a5e2-0800200c9a66",
    "STATUS": "7d32c0f0-bef5-11e3-b1b6-0800200c9a66",
}

ENABLE_MEASUREMENT = bytes.fromhex("01 00 00 00 00 00")


def decode(name, data):
    if not data:
        print(f"{name}: No data received.")
        return

    print(f"\nNotification from {name}: {data.hex()}")

    try:
        if name == "FEV1":
            if len(data) >= 2:
                raw_val = int.from_bytes(data[:2], 'little')
                val = raw_val / 100.0
                print(f"FEV1: {val:.2f} L")
        elif name == "FVC":
            if len(data) >= 2:
                raw_val = int.from_bytes(data[:2], 'little')
                val = raw_val / 100.0
                print(f"FVC: {val:.2f} L")
        elif name == "FEV1_FVC":
            if len(data) >= 1:
                val = data[0] if len(data) == 1 else int.from_bytes(
                    data[:2], 'little')
                print(f"FEV1/FVC Ratio: {val}%")
        elif name == "PEF":
            if len(data) >= 2:
                raw_val = int.from_bytes(data[:2], 'little')
                val = raw_val / 10.0
                print(f"PEF: {val:.1f} L/s")
        elif name == "STATUS":
            if len(data) >= 1:
                status = data[0]
                status_map = {0: "Idle", 1: "Measuring", 2: "Test Complete"}
                print(f"Status: {status_map.get(status, 'Unknown')}")
    except Exception as e:
        print(f"Error decoding {name}: {e}")


async def main():
    device = await BleakScanner.find_device_by_address(DEVICE_ADDRESS, timeout=20.0)
    if not device:
        print("Device not found.")
        return

    async with BleakClient(device) as client:
        print("Connected to device.")

        for name, uuid in UUIDS.items():
            if name == "COMMAND":
                continue

            await client.start_notify(uuid, lambda sender, data, n=name: decode(n, data))
            print(f"Subscribed to {name} notifications.")

        await client.write_gatt_char(UUIDS["COMMAND"], ENABLE_MEASUREMENT, response=False)
        print("Measurement enabled. Blow into the spirometer.")

        await asyncio.sleep(60)  # Listen for 60 seconds

        for uuid in UUIDS.values():
            if uuid == UUIDS["COMMAND"]:
                continue
            await client.stop_notify(uuid)
        print("Stopped notifications.")

if __name__ == "__main__":
    asyncio.run(main())
